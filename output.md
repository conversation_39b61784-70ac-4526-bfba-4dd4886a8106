# TLS/SSL Configuration Issues
**Project:** MBH
**Target:** mbh.hu:443
**Scan date:** 2025-09-18 18:10:26 UTC

## Summary
| Severity | Count |
|---|---:|
| CRITICAL | 2 |
| MEDIUM | 2 |
| LOW | 10 |
| INFO | 209 |
| OK | 100 |
| WARN | 1 |

## Findings
### 1. Self-Signed Certificate
- **Severity:** CRITICAL
- **Endpoint:** mbh.hu/**********:443

**Detail**

Some certificate trust checks failed -> Java (self signed CA in chain)  , OK -> Mozilla Microsoft Linux Apple

**Remediation**

Use a certificate issued by a trusted CA.

---

### 2. Self-Signed Certificate
- **Severity:** CRITICAL
- **Endpoint:** mbh.hu/*************:443

**Detail**

Some certificate trust checks failed -> Java (self signed CA in chain)  , OK -> Mozilla Microsoft Linux Apple

**Remediation**

Use a certificate issued by a trusted CA.

---

### 3. TLS/SSL Misconfiguration
- **Severity:** MEDIUM
- **Endpoint:** mbh.hu/**********:443

**Detail**

--

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 4. TLS/SSL Misconfiguration
- **Severity:** MEDIUM
- **Endpoint:** mbh.hu/*************:443

**Detail**

--

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 5. Certificate Signed with SHA-1
- **Severity:** LOW
- **Endpoint:** mbh.hu/**********:443

**Detail**

ECDSA+SHA256 ECDSA+SHA384 ECDSA+SHA512 ECDSA+SHA224 ECDSA+SHA1

**Remediation**

Reissue the certificate using SHA-256 or stronger.

---

### 6. OCSP Stapling Not Enabled
- **Severity:** LOW
- **Endpoint:** mbh.hu/**********:443

**Detail**

not offered

**Remediation**

Enable OCSP stapling to improve revocation checking performance.

---

### 7. TLS/SSL Misconfiguration
- **Severity:** LOW
- **Endpoint:** mbh.hu/**********:443

**Detail**

--

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 8. TLS/SSL Misconfiguration
- **Severity:** LOW
- **Endpoint:** mbh.hu/**********:443

**Detail**

yes

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 9. Missing or Weak HSTS
- **Severity:** LOW
- **Endpoint:** mbh.hu/**********:443

**Detail**

not offered

**Remediation**

Enable HSTS with a sufficiently long max-age and includeSubDomains where appropriate.

---

### 10. Certificate Signed with SHA-1
- **Severity:** LOW
- **Endpoint:** mbh.hu/*************:443

**Detail**

ECDSA+SHA256 ECDSA+SHA384 ECDSA+SHA512 ECDSA+SHA224 ECDSA+SHA1

**Remediation**

Reissue the certificate using SHA-256 or stronger.

---

### 11. OCSP Stapling Not Enabled
- **Severity:** LOW
- **Endpoint:** mbh.hu/*************:443

**Detail**

not offered

**Remediation**

Enable OCSP stapling to improve revocation checking performance.

---

### 12. TLS/SSL Misconfiguration
- **Severity:** LOW
- **Endpoint:** mbh.hu/*************:443

**Detail**

--

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 13. TLS/SSL Misconfiguration
- **Severity:** LOW
- **Endpoint:** mbh.hu/*************:443

**Detail**

yes

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 14. Missing or Weak HSTS
- **Severity:** LOW
- **Endpoint:** mbh.hu/*************:443

**Detail**

not offered

**Remediation**

Enable HSTS with a sufficiently long max-age and includeSubDomains where appropriate.

---

### 15. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

HTTP

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 16. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

No 128 cipher limit bug

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 17. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

not offered

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 18. Legacy Protocol Enabled (TLS 1.1)
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

not offered

**Remediation**

Disable TLS 1.1; enforce TLS 1.2+.

---

### 19. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

not offered + downgraded to weaker protocol

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 20. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

offered with h2, http/1.1 (advertised)

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 21. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

http/1.1

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 22. Weak/Deprecated Cipher Suites
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

not offered

**Remediation**

Remove weak ciphers (e.g., RC4, 3DES) and prefer modern AEAD ciphers like TLS_AES_* or ECDHE+AES-GCM.

---

### 23. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

not offered

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 24. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

not offered

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 25. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

ECDHE-ECDSA-AES128-GCM-SHA256 ECDHE-ECDSA-AES256-GCM-SHA384

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 26. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

ECDHE-ECDSA-AES256-GCM-SHA384 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 27. Insecure Renegotiation
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

'server name/#0' 'EC point formats/#11' 'renegotiation info/#65281' 'session ticket/#35' 'next protocol/#13172' 'application layer protocol negotiation/#16' 'extended master secret/#23'

**Remediation**

Disable insecure renegotiation; ensure only secure renegotiation is permitted.

---

### 28. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

valid for 86400 seconds only (<daily)

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 29. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

yes

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 30. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

not supported

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 31. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

not supported

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 32. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

random

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 33. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

N/A

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 34. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

none

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 35. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

1

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 36. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

Digital Signature, Key Agreement

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 37. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

TLS Web Server Authentication

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 38. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

02F2063D6ADC289B7950B23DDD0A

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 39. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

14

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 40. Certificate Signed with SHA-1
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

C2BAF7DF881786086615DBA22CD526AD0D69BE92

**Remediation**

Reissue the certificate using SHA-256 or stronger.

---

### 41. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

BED0CA5437F016A47E447B8F0B3976BE7BBAD3A9074945393D96C953CB80559C

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 42. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

-----BEGIN CERTIFICATE-----
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
-----END CERTIFICATE-----

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 43. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

mbhbank.hu

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 44. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

mbh.hu www.mbh.hu bpnyugdij.hu www.bpnyugdij.hu mbhbank.eu www.mbhbank.eu

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 45. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

no

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 46. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

2025-05-23 08:20

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 47. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

not present

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 48. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

http://eqtlsca2018-crl1.e-szigno.hu/eqtlsca2018.crl http://eqtlsca2018-crl2.e-szigno.hu/eqtlsca2018.crl http://eqtlsca2018-crl3.e-szigno.hu/eqtlsca2018.crl

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 49. OCSP Stapling Not Enabled
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

http://eqtlsca2018-ocsp1.e-szigno.hu http://eqtlsca2018-ocsp2.e-szigno.hu http://eqtlsca2018-ocsp3.e-szigno.hu

**Remediation**

Enable OCSP stapling to improve revocation checking performance.

---

### 50. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

--

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 51. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

4

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 52. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

e-Szigno Qualified TLS CA 2018 (Microsec Ltd. from HU)

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 53. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

-----BEGIN CERTIFICATE-----
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
-----END CERTIFICATE-----

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 54. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

BED0CA5437F016A47E447B8F0B3976BE7BBAD3A9074945393D96C953CB80559C

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 55. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

2025-05-23 08:20

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 56. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

bpnyugdij.hu <--  e-Szigno Qualified TLS CA 2018

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 57. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

-----BEGIN CERTIFICATE-----
MIID+jCCAuKgAwIBAgINAPPm0I03jXAq8keuCjANBgkqhkiG9w0BAQsFADCBgjELMAkGA1UEBhMCSFUxETAPBgNVBAcMCEJ1ZGFwZXN0MRYwFAYDVQQKDA1NaWNyb3NlYyBMdGQuMScwJQYDVQQDDB5NaWNyb3NlYyBlLVN6aWdubyBSb290IENBIDIwMDkxHzAdBgkqhkiG9w0BCQEWEGluZm9AZS1zemlnbm8uaHUwHhcNMjMwODI0MTYwMDAwWhcNMjUxMjMwMTcwMDAwWjB6MQswCQYDVQQGEwJIVTERMA8GA1UEBwwIQnVkYXBlc3QxFjAUBgNVBAoMDU1pY3Jvc2VjIEx0ZC4xFzAVBgNVBGEMDlZBVEhVLTIzNTg0NDk3MScwJQYDVQQDDB5lLVN6aWdubyBRdWFsaWZpZWQgVExTIENBIDIwMTgwWTATBgcqhkjOPQIBBggqhkjOPQMBBwNCAASgco960wV76MEdWtug1L/YHhkrZqorU/aHRUrHy/bW+MwlYCE+SiUk3KK6r/V/YAwMz+zqGOLeq1iB9osx34aso4IBPzCCATswDwYDVR0TAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMCAQYwHQYDVR0lBBYwFAYIKwYBBQUHAwEGCCsGAQUFBwMCMBEGA1UdIAQKMAgwBgYEVR0gADAdBgNVHQ4EFgQU2Y0YuKwjtjuMF8nrbrkXAGkNoIwwHwYDVR0jBBgwFoAUyw/G30JDzD3LtUgjoRp6piq7NGgwNgYDVR0fBC8wLTAroCmgJ4YlaHR0cDovL2NybC5lLXN6aWduby5odS9yb290Y2EyMDA5LmNybDBuBggrBgEFBQcBAQRiMGAwKwYIKwYBBQUHMAGGH2h0dHA6Ly9yb290b2NzcDIwMDkuZS1zemlnbm8uaHUwMQYIKwYBBQUHMAKGJWh0dHA6Ly93d3cuZS1zemlnbm8uaHUvcm9vdGNhMjAwOS5jcnQwDQYJKoZIhvcNAQELBQADggEBAOkl6c8v6nmjEeJ7w60rmb8wr/V+us9FIpVXI2DUN+uphRVHaAm3JYBzbLI/NUULPD025oP7/+ni3YAyWJQjvmZZR5b2LZiZ5PRw2aU3fxPhAzKP8AsT2ghE7CcJ3QMHl+rAhM6Cv/iA+1TCOjt848LCcv86tzVB+1o9J7wBirW+VLCVdG9TA71ewYzEF8BFKCGxMCWLxSkZG8W7/oKm0tdHlRT+7D5jH++rDAqBCi8OUEHldOoJGERur31zhq4rTHT9lGeBB2ZCFgsfnIXakvP9nmPBF+Ny1y46FtjR2FLxh2l4y/umtKZWgt5HtY5/Lsy2KOs94Do99yaNLHXzQi4=
-----END CERTIFICATE-----

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 58. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

6A48E734AC6F067140C928ADBBCC4492469D416DE2D3C9A7A197D62370EAC0E2

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 59. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

2023-08-24 16:00

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 60. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

e-Szigno Qualified TLS CA 2018 <--  <EMAIL>

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 61. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

-----BEGIN CERTIFICATE-----
MIIECjCCAvKgAwIBAgIJAMJ+QwRORz8ZMA0GCSqGSIb3DQEBCwUAMIGCMQswCQYDVQQGEwJIVTERMA8GA1UEBwwIQnVkYXBlc3QxFjAUBgNVBAoMDU1pY3Jvc2VjIEx0ZC4xJzAlBgNVBAMMHk1pY3Jvc2VjIGUtU3ppZ25vIFJvb3QgQ0EgMjAwOTEfMB0GCSqGSIb3DQEJARYQaW5mb0BlLXN6aWduby5odTAeFw0wOTA2MTYxMTMwMThaFw0yOTEyMzAxMTMwMThaMIGCMQswCQYDVQQGEwJIVTERMA8GA1UEBwwIQnVkYXBlc3QxFjAUBgNVBAoMDU1pY3Jvc2VjIEx0ZC4xJzAlBgNVBAMMHk1pY3Jvc2VjIGUtU3ppZ25vIFJvb3QgQ0EgMjAwOTEfMB0GCSqGSIb3DQEJARYQaW5mb0BlLXN6aWduby5odTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAOn4j/NjrdqG2KfgQvvPkd6mJviZpWNwrZuuyjNAfW2WbqEORO7hE52UQlKavXWFdCyoDh2Tthi3jCyoz/tccbna7P7ofo/kLx2yqHWH2Leh5TvPmUpG0IMZfcChEhyVbUr02MelTTMuhTlAdX4UfIASmFDHQWe4oIBhVKZsTh/gnQ4H6cm6M+f+wFUoLAKApxn1ntxVUwOXewdI/5n7N4okxFnMUBBjjqqpGrCEGob5X7uxUG6k0QrM1XF+H6cbfPVTbiJfyyvm1HxdrtbCxkzlBQHZ7Vf8wSN5/PrIJIOV87VqUQHQd9bpEqH5GoP7ghu5sJf0dgYzQ0mg/wu1+rUCAwEAAaOBgDB+MA8GA1UdEwEB/wQFMAMBAf8wDgYDVR0PAQH/BAQDAgEGMB0GA1UdDgQWBBTLD8bfQkPMPcu1SCOhGnqmKrs0aDAfBgNVHSMEGDAWgBTLD8bfQkPMPcu1SCOhGnqmKrs0aDAbBgNVHREEFDASgRBpbmZvQGUtc3ppZ25vLmh1MA0GCSqGSIb3DQEBCwUAA4IBAQDJ0Q5eLtXMs3w+y/w9/w0olZMEyL/azXm4Q5DwpL7v8u8hmLzU1F0G9u5C7DBsoKqpyvGvivo/C3NqPuouQH4frlRheesuCDfXI/OMn74dseGkddug4lQUsbocKaQY9hK6ohQU4zE1yED/t+AFdlfBHFny+L/k7SViXITwfn4fs775tyERzAMBVnCnEJIeGzSBHq2cGsMEPO0CYdYeBvNfOofyK/FFh+U9rNHHV4S9a67c2Pm2G2JwCz02yULyMtd6YebS2z3PyKnJm9zbWETXbzivf3jTo60adbocwTZ8jx5tHMN1Rq41Bab2XD0h7lbwyYIiLXpUq3DDfSJlgnCW
-----END CERTIFICATE-----

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 62. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

3C5F81FEA5FAB82C64BFA2EAECAFCDE8E077FC8620A7CAE537163DF36EDBF378

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 63. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

2009-06-16 11:30

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 64. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

<EMAIL> <--  <EMAIL>

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 65. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

301 Moved Permanently ('/')

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 66. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

0 seconds from localtime

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 67. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

1758190682

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 68. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

No support for HTTP Public Key Pinning

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 69. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

awselb/2.0

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 70. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

No application banner found

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 71. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

0 at '/' (30x detected, better try target URL of 30x)

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 72. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

--

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 73. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443
- **CVE:** CVE-2016-0800 CVE-2016-0703

**Detail**

no RSA certificate, can't be used with SSLv2 elsewhere

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 74. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 75. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 76. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 77. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 78. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 79. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 80. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 81. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 82. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 83. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 84. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 85. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

No connection

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 86. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

No connection

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 87. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

No connection

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 88. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 89. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 90. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 91. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 92. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 93. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 94. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 95. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 96. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 97. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

No connection

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 98. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 99. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 100. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 101. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 102. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 103. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 104. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 105. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 106. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 107. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 108. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 109. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

SSL Labs's 'SSL Server Rating Guide' (version 2009q from 2020-01-30)

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 110. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

https://github.com/ssllabs/research/wiki/SSL-Server-Rating-Guide

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 111. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

100

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 112. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

30

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 113. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

100

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 114. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

30

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 115. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

90

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 116. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

36

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 117. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

96

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 118. Missing or Weak HSTS
- **Severity:** INFO
- **Endpoint:** mbh.hu/**********:443

**Detail**

Grade capped to A. HSTS is not offered

**Remediation**

Enable HSTS with a sufficiently long max-age and includeSubDomains where appropriate.

---

### 119. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

HTTP

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 120. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

No 128 cipher limit bug

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 121. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

not offered

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 122. Legacy Protocol Enabled (TLS 1.1)
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

not offered

**Remediation**

Disable TLS 1.1; enforce TLS 1.2+.

---

### 123. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

not offered + downgraded to weaker protocol

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 124. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

offered with h2, http/1.1 (advertised)

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 125. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

http/1.1

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 126. Weak/Deprecated Cipher Suites
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

not offered

**Remediation**

Remove weak ciphers (e.g., RC4, 3DES) and prefer modern AEAD ciphers like TLS_AES_* or ECDHE+AES-GCM.

---

### 127. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

not offered

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 128. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

not offered

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 129. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

ECDHE-ECDSA-AES128-GCM-SHA256 ECDHE-ECDSA-AES256-GCM-SHA384

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 130. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

ECDHE-ECDSA-AES256-GCM-SHA384 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 131. Insecure Renegotiation
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

'server name/#0' 'EC point formats/#11' 'renegotiation info/#65281' 'session ticket/#35' 'next protocol/#13172' 'application layer protocol negotiation/#16' 'extended master secret/#23'

**Remediation**

Disable insecure renegotiation; ensure only secure renegotiation is permitted.

---

### 132. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

valid for 86400 seconds only (<daily)

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 133. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

yes

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 134. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

supported

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 135. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

not supported

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 136. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

random

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 137. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

N/A

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 138. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

none

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 139. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

1

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 140. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

Digital Signature, Key Agreement

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 141. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

TLS Web Server Authentication

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 142. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

02F2063D6ADC289B7950B23DDD0A

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 143. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

14

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 144. Certificate Signed with SHA-1
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

C2BAF7DF881786086615DBA22CD526AD0D69BE92

**Remediation**

Reissue the certificate using SHA-256 or stronger.

---

### 145. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

BED0CA5437F016A47E447B8F0B3976BE7BBAD3A9074945393D96C953CB80559C

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 146. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

-----BEGIN CERTIFICATE-----
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
-----END CERTIFICATE-----

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 147. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

mbhbank.hu

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 148. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

mbh.hu www.mbh.hu bpnyugdij.hu www.bpnyugdij.hu mbhbank.eu www.mbhbank.eu

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 149. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

no

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 150. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

2025-05-23 08:20

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 151. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

not present

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 152. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

http://eqtlsca2018-crl1.e-szigno.hu/eqtlsca2018.crl http://eqtlsca2018-crl2.e-szigno.hu/eqtlsca2018.crl http://eqtlsca2018-crl3.e-szigno.hu/eqtlsca2018.crl

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 153. OCSP Stapling Not Enabled
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

http://eqtlsca2018-ocsp1.e-szigno.hu http://eqtlsca2018-ocsp2.e-szigno.hu http://eqtlsca2018-ocsp3.e-szigno.hu

**Remediation**

Enable OCSP stapling to improve revocation checking performance.

---

### 154. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

--

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 155. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

4

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 156. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

e-Szigno Qualified TLS CA 2018 (Microsec Ltd. from HU)

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 157. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

-----BEGIN CERTIFICATE-----
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
-----END CERTIFICATE-----

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 158. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

BED0CA5437F016A47E447B8F0B3976BE7BBAD3A9074945393D96C953CB80559C

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 159. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

2025-05-23 08:20

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 160. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

bpnyugdij.hu <--  e-Szigno Qualified TLS CA 2018

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 161. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

-----BEGIN CERTIFICATE-----
MIID+jCCAuKgAwIBAgINAPPm0I03jXAq8keuCjANBgkqhkiG9w0BAQsFADCBgjELMAkGA1UEBhMCSFUxETAPBgNVBAcMCEJ1ZGFwZXN0MRYwFAYDVQQKDA1NaWNyb3NlYyBMdGQuMScwJQYDVQQDDB5NaWNyb3NlYyBlLVN6aWdubyBSb290IENBIDIwMDkxHzAdBgkqhkiG9w0BCQEWEGluZm9AZS1zemlnbm8uaHUwHhcNMjMwODI0MTYwMDAwWhcNMjUxMjMwMTcwMDAwWjB6MQswCQYDVQQGEwJIVTERMA8GA1UEBwwIQnVkYXBlc3QxFjAUBgNVBAoMDU1pY3Jvc2VjIEx0ZC4xFzAVBgNVBGEMDlZBVEhVLTIzNTg0NDk3MScwJQYDVQQDDB5lLVN6aWdubyBRdWFsaWZpZWQgVExTIENBIDIwMTgwWTATBgcqhkjOPQIBBggqhkjOPQMBBwNCAASgco960wV76MEdWtug1L/YHhkrZqorU/aHRUrHy/bW+MwlYCE+SiUk3KK6r/V/YAwMz+zqGOLeq1iB9osx34aso4IBPzCCATswDwYDVR0TAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMCAQYwHQYDVR0lBBYwFAYIKwYBBQUHAwEGCCsGAQUFBwMCMBEGA1UdIAQKMAgwBgYEVR0gADAdBgNVHQ4EFgQU2Y0YuKwjtjuMF8nrbrkXAGkNoIwwHwYDVR0jBBgwFoAUyw/G30JDzD3LtUgjoRp6piq7NGgwNgYDVR0fBC8wLTAroCmgJ4YlaHR0cDovL2NybC5lLXN6aWduby5odS9yb290Y2EyMDA5LmNybDBuBggrBgEFBQcBAQRiMGAwKwYIKwYBBQUHMAGGH2h0dHA6Ly9yb290b2NzcDIwMDkuZS1zemlnbm8uaHUwMQYIKwYBBQUHMAKGJWh0dHA6Ly93d3cuZS1zemlnbm8uaHUvcm9vdGNhMjAwOS5jcnQwDQYJKoZIhvcNAQELBQADggEBAOkl6c8v6nmjEeJ7w60rmb8wr/V+us9FIpVXI2DUN+uphRVHaAm3JYBzbLI/NUULPD025oP7/+ni3YAyWJQjvmZZR5b2LZiZ5PRw2aU3fxPhAzKP8AsT2ghE7CcJ3QMHl+rAhM6Cv/iA+1TCOjt848LCcv86tzVB+1o9J7wBirW+VLCVdG9TA71ewYzEF8BFKCGxMCWLxSkZG8W7/oKm0tdHlRT+7D5jH++rDAqBCi8OUEHldOoJGERur31zhq4rTHT9lGeBB2ZCFgsfnIXakvP9nmPBF+Ny1y46FtjR2FLxh2l4y/umtKZWgt5HtY5/Lsy2KOs94Do99yaNLHXzQi4=
-----END CERTIFICATE-----

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 162. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

6A48E734AC6F067140C928ADBBCC4492469D416DE2D3C9A7A197D62370EAC0E2

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 163. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

2023-08-24 16:00

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 164. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

e-Szigno Qualified TLS CA 2018 <--  <EMAIL>

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 165. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

-----BEGIN CERTIFICATE-----
MIIECjCCAvKgAwIBAgIJAMJ+QwRORz8ZMA0GCSqGSIb3DQEBCwUAMIGCMQswCQYDVQQGEwJIVTERMA8GA1UEBwwIQnVkYXBlc3QxFjAUBgNVBAoMDU1pY3Jvc2VjIEx0ZC4xJzAlBgNVBAMMHk1pY3Jvc2VjIGUtU3ppZ25vIFJvb3QgQ0EgMjAwOTEfMB0GCSqGSIb3DQEJARYQaW5mb0BlLXN6aWduby5odTAeFw0wOTA2MTYxMTMwMThaFw0yOTEyMzAxMTMwMThaMIGCMQswCQYDVQQGEwJIVTERMA8GA1UEBwwIQnVkYXBlc3QxFjAUBgNVBAoMDU1pY3Jvc2VjIEx0ZC4xJzAlBgNVBAMMHk1pY3Jvc2VjIGUtU3ppZ25vIFJvb3QgQ0EgMjAwOTEfMB0GCSqGSIb3DQEJARYQaW5mb0BlLXN6aWduby5odTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAOn4j/NjrdqG2KfgQvvPkd6mJviZpWNwrZuuyjNAfW2WbqEORO7hE52UQlKavXWFdCyoDh2Tthi3jCyoz/tccbna7P7ofo/kLx2yqHWH2Leh5TvPmUpG0IMZfcChEhyVbUr02MelTTMuhTlAdX4UfIASmFDHQWe4oIBhVKZsTh/gnQ4H6cm6M+f+wFUoLAKApxn1ntxVUwOXewdI/5n7N4okxFnMUBBjjqqpGrCEGob5X7uxUG6k0QrM1XF+H6cbfPVTbiJfyyvm1HxdrtbCxkzlBQHZ7Vf8wSN5/PrIJIOV87VqUQHQd9bpEqH5GoP7ghu5sJf0dgYzQ0mg/wu1+rUCAwEAAaOBgDB+MA8GA1UdEwEB/wQFMAMBAf8wDgYDVR0PAQH/BAQDAgEGMB0GA1UdDgQWBBTLD8bfQkPMPcu1SCOhGnqmKrs0aDAfBgNVHSMEGDAWgBTLD8bfQkPMPcu1SCOhGnqmKrs0aDAbBgNVHREEFDASgRBpbmZvQGUtc3ppZ25vLmh1MA0GCSqGSIb3DQEBCwUAA4IBAQDJ0Q5eLtXMs3w+y/w9/w0olZMEyL/azXm4Q5DwpL7v8u8hmLzU1F0G9u5C7DBsoKqpyvGvivo/C3NqPuouQH4frlRheesuCDfXI/OMn74dseGkddug4lQUsbocKaQY9hK6ohQU4zE1yED/t+AFdlfBHFny+L/k7SViXITwfn4fs775tyERzAMBVnCnEJIeGzSBHq2cGsMEPO0CYdYeBvNfOofyK/FFh+U9rNHHV4S9a67c2Pm2G2JwCz02yULyMtd6YebS2z3PyKnJm9zbWETXbzivf3jTo60adbocwTZ8jx5tHMN1Rq41Bab2XD0h7lbwyYIiLXpUq3DDfSJlgnCW
-----END CERTIFICATE-----

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 166. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

3C5F81FEA5FAB82C64BFA2EAECAFCDE8E077FC8620A7CAE537163DF36EDBF378

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 167. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

2009-06-16 11:30

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 168. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

<EMAIL> <--  <EMAIL>

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 169. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

301 Moved Permanently ('/')

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 170. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

0 seconds from localtime

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 171. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

1758190739

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 172. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

No support for HTTP Public Key Pinning

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 173. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

awselb/2.0

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 174. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

No application banner found

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 175. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

0 at '/' (30x detected, better try target URL of 30x)

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 176. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

--

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 177. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443
- **CVE:** CVE-2016-0800 CVE-2016-0703

**Detail**

no RSA certificate, can't be used with SSLv2 elsewhere

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 178. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 179. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 180. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 181. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 182. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 183. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 184. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 185. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 186. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 187. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 188. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 189. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

No connection

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 190. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

No connection

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 191. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

No connection

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 192. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 193. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 194. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 195. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 196. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 197. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 198. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 199. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 200. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 201. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

No connection

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 202. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 203. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 204. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 205. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 206. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 207. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 208. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 209. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 210. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 211. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 212. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 213. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

SSL Labs's 'SSL Server Rating Guide' (version 2009q from 2020-01-30)

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 214. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

https://github.com/ssllabs/research/wiki/SSL-Server-Rating-Guide

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 215. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

100

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 216. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

30

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 217. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

100

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 218. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

30

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 219. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

90

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 220. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

36

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 221. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

96

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 222. Missing or Weak HSTS
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

Grade capped to A. HSTS is not offered

**Remediation**

Enable HSTS with a sufficiently long max-age and includeSubDomains where appropriate.

---

### 223. TLS/SSL Misconfiguration
- **Severity:** INFO
- **Endpoint:** mbh.hu/*************:443

**Detail**

116

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 224. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443

**Detail**

not offered

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 225. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443

**Detail**

not offered

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 226. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443

**Detail**

offered

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 227. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443

**Detail**

h2

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 228. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443

**Detail**

not offered

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 229. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443

**Detail**

not offered

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 230. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443

**Detail**

not offered

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 231. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443

**Detail**

not offered

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 232. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443

**Detail**

offered

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 233. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443

**Detail**

server

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 234. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443

**Detail**

TLSv1.2   xc02b   ECDHE-ECDSA-AES128-GCM-SHA256     ECDH 256   AESGCM      128      TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 235. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443

**Detail**

TLSv1.2   xc02c   ECDHE-ECDSA-AES256-GCM-SHA384     ECDH 256   AESGCM      256      TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 236. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443

**Detail**

server

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 237. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443

**Detail**

offered

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 238. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443

**Detail**

prime256v1 secp384r1 secp521r1

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 239. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443

**Detail**

ECDSA with SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 240. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443

**Detail**

EC 256 bits (curve P-256)

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 241. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443

**Detail**

bpnyugdij.hu

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 242. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443

**Detail**

Ok via SAN (SNI mandatory)

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 243. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443

**Detail**

246 >= 60 days

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 244. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443

**Detail**

2026-05-23 08:20

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 245. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443

**Detail**

certificate has no extended life time according to browser forum

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 246. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443

**Detail**

yes (certificate extension)

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 247. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443

**Detail**

2026-05-23 08:20

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 248. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443

**Detail**

ok > 40 days

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 249. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443

**Detail**

2025-12-30 17:00

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 250. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443

**Detail**

ok > 40 days

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 251. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443

**Detail**

2029-12-30 11:30

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 252. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443

**Detail**

ok > 40 days

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 253. OCSP Stapling Not Enabled
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443

**Detail**

intermediate certificate(s) is/are ok

**Remediation**

Enable OCSP stapling to improve revocation checking performance.

---

### 254. OpenSSL Heartbleed
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443
- **CVE:** CVE-2014-0160

**Detail**

not vulnerable, no heartbeat extension

**Remediation**

Upgrade OpenSSL to a non-vulnerable version and restart services.

---

### 255. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443
- **CVE:** CVE-2014-0224

**Detail**

not vulnerable

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 256. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443
- **CVE:** CVE-2016-9244

**Detail**

not vulnerable

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 257. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443
- **CVE:** CVE-2017-17382 CVE-2017-17427 CVE-2017-17428 CVE-2017-13098 CVE-2017-1000385 CVE-2017-13099 CVE-2016-6883 CVE-2012-5081 CVE-2017-6168

**Detail**

not vulnerable, no RSA key transport cipher

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 258. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443

**Detail**

supported

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 259. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443
- **CVE:** CVE-2011-1473

**Detail**

not vulnerable

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 260. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443
- **CVE:** CVE-2012-4929

**Detail**

not vulnerable

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 261. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443
- **CVE:** CVE-2013-3587

**Detail**

not vulnerable, no gzip/deflate/compress/br HTTP compression  - only supplied '/' tested

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 262. POODLE / SSLv3 Supported
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443
- **CVE:** CVE-2014-3566

**Detail**

not vulnerable, no SSLv3

**Remediation**

Disable SSLv3 and use TLS 1.2+ only.

---

### 263. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443

**Detail**

no protocol below TLS 1.2 offered

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 264. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443
- **CVE:** CVE-2016-2183 CVE-2016-6329

**Detail**

not vulnerable

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 265. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443
- **CVE:** CVE-2015-0204

**Detail**

not vulnerable

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 266. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443
- **CVE:** CVE-2016-0800 CVE-2016-0703

**Detail**

not vulnerable on this host and port

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 267. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443
- **CVE:** CVE-2015-4000

**Detail**

not vulnerable, no DH EXPORT ciphers,

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 268. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443
- **CVE:** CVE-2015-4000

**Detail**

no DH key with <= TLS 1.2

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 269. POODLE / SSLv3 Supported
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443
- **CVE:** CVE-2011-3389

**Detail**

not vulnerable, no SSL3 or TLS1

**Remediation**

Disable SSLv3 and use TLS 1.2+ only.

---

### 270. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443
- **CVE:** CVE-2013-0169

**Detail**

not vulnerable

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 271. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443
- **CVE:** CVE-2014-6321

**Detail**

not vulnerable - doesn't seem to be IIS 8.x

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 272. Weak/Deprecated Cipher Suites
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443
- **CVE:** CVE-2013-2566 CVE-2015-2808

**Detail**

not vulnerable

**Remediation**

Remove weak ciphers (e.g., RC4, 3DES) and prefer modern AEAD ciphers like TLS_AES_* or ECDHE+AES-GCM.

---

### 273. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/**********:443

**Detail**

A

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 274. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443

**Detail**

not offered

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 275. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443

**Detail**

not offered

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 276. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443

**Detail**

offered

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 277. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443

**Detail**

h2

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 278. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443

**Detail**

not offered

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 279. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443

**Detail**

not offered

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 280. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443

**Detail**

not offered

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 281. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443

**Detail**

not offered

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 282. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443

**Detail**

offered

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 283. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443

**Detail**

server

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 284. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443

**Detail**

TLSv1.2   xc02b   ECDHE-ECDSA-AES128-GCM-SHA256     ECDH 256   AESGCM      128      TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 285. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443

**Detail**

TLSv1.2   xc02c   ECDHE-ECDSA-AES256-GCM-SHA384     ECDH 256   AESGCM      256      TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 286. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443

**Detail**

server

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 287. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443

**Detail**

offered

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 288. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443

**Detail**

prime256v1 secp384r1 secp521r1

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 289. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443

**Detail**

ECDSA with SHA256

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 290. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443

**Detail**

EC 256 bits (curve P-256)

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 291. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443

**Detail**

bpnyugdij.hu

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 292. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443

**Detail**

Ok via SAN (SNI mandatory)

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 293. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443

**Detail**

246 >= 60 days

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 294. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443

**Detail**

2026-05-23 08:20

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 295. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443

**Detail**

certificate has no extended life time according to browser forum

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 296. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443

**Detail**

yes (certificate extension)

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 297. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443

**Detail**

2026-05-23 08:20

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 298. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443

**Detail**

ok > 40 days

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 299. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443

**Detail**

2025-12-30 17:00

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 300. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443

**Detail**

ok > 40 days

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 301. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443

**Detail**

2029-12-30 11:30

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 302. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443

**Detail**

ok > 40 days

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 303. OCSP Stapling Not Enabled
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443

**Detail**

intermediate certificate(s) is/are ok

**Remediation**

Enable OCSP stapling to improve revocation checking performance.

---

### 304. OpenSSL Heartbleed
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443
- **CVE:** CVE-2014-0160

**Detail**

not vulnerable, no heartbeat extension

**Remediation**

Upgrade OpenSSL to a non-vulnerable version and restart services.

---

### 305. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443
- **CVE:** CVE-2014-0224

**Detail**

not vulnerable

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 306. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443
- **CVE:** CVE-2016-9244

**Detail**

not vulnerable

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 307. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443
- **CVE:** CVE-2017-17382 CVE-2017-17427 CVE-2017-17428 CVE-2017-13098 CVE-2017-1000385 CVE-2017-13099 CVE-2016-6883 CVE-2012-5081 CVE-2017-6168

**Detail**

not vulnerable, no RSA key transport cipher

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 308. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443

**Detail**

supported

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 309. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443
- **CVE:** CVE-2011-1473

**Detail**

not vulnerable

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 310. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443
- **CVE:** CVE-2012-4929

**Detail**

not vulnerable

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 311. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443
- **CVE:** CVE-2013-3587

**Detail**

not vulnerable, no gzip/deflate/compress/br HTTP compression  - only supplied '/' tested

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 312. POODLE / SSLv3 Supported
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443
- **CVE:** CVE-2014-3566

**Detail**

not vulnerable, no SSLv3

**Remediation**

Disable SSLv3 and use TLS 1.2+ only.

---

### 313. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443

**Detail**

no protocol below TLS 1.2 offered

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 314. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443
- **CVE:** CVE-2016-2183 CVE-2016-6329

**Detail**

not vulnerable

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 315. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443
- **CVE:** CVE-2015-0204

**Detail**

not vulnerable

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 316. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443
- **CVE:** CVE-2016-0800 CVE-2016-0703

**Detail**

not vulnerable on this host and port

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 317. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443
- **CVE:** CVE-2015-4000

**Detail**

not vulnerable, no DH EXPORT ciphers,

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 318. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443
- **CVE:** CVE-2015-4000

**Detail**

no DH key with <= TLS 1.2

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 319. POODLE / SSLv3 Supported
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443
- **CVE:** CVE-2011-3389

**Detail**

not vulnerable, no SSL3 or TLS1

**Remediation**

Disable SSLv3 and use TLS 1.2+ only.

---

### 320. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443
- **CVE:** CVE-2013-0169

**Detail**

not vulnerable

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 321. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443
- **CVE:** CVE-2014-6321

**Detail**

not vulnerable - doesn't seem to be IIS 8.x

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 322. Weak/Deprecated Cipher Suites
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443
- **CVE:** CVE-2013-2566 CVE-2015-2808

**Detail**

not vulnerable

**Remediation**

Remove weak ciphers (e.g., RC4, 3DES) and prefer modern AEAD ciphers like TLS_AES_* or ECDHE+AES-GCM.

---

### 323. TLS/SSL Misconfiguration
- **Severity:** OK
- **Endpoint:** mbh.hu/*************:443

**Detail**

A

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---

### 324. TLS/SSL Misconfiguration
- **Severity:** WARN
- **Endpoint:** mbh.hu/:443

**Detail**

No engine or GOST support via engine with your /usr/bin/openssl

**Remediation**

Review and update TLS/SSL configuration according to current best practices.

---
