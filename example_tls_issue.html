<h2 id="pt-3-insecure-ssltls-configuration">PT-3: Insecure SSL/TLS
Configuration</h2>
<table style="width:100%;">
<colgroup>
<col style="width: 29%" />
<col style="width: 13%" />
<col style="width: 32%" />
<col style="width: 9%" />
<col style="width: 2%" />
<col style="width: 12%" />
</colgroup>
<thead>
<tr>
<th><strong>Target(s)</strong></th>
<th rowspan="3"><strong>Vulnerability Type</strong></th>
<th rowspan="3">A02:2021-Cryptographic Failures</th>
<th><blockquote>
<p><strong>Risk</strong></p>
</blockquote></th>
<th style="text-align: center;">●</th>
<th>Medium</th>
</tr>
<tr>
<th rowspan="2">%%TARGET-PLACEHOLDER%%</th>
<th><blockquote>
<p><strong>Impact</strong></p>
</blockquote></th>
<th style="text-align: center;">●</th>
<th>High</th>
</tr>
<tr>
<th><blockquote>
<p><strong>Likelihood</strong></p>
</blockquote></th>
<th style="text-align: center;">●</th>
<th>Low</th>
</tr>
</thead>
<tbody>
</tbody>
</table>
<h3 id="description">Description</h3>
<p>We identified that the server<mark>s</mark> in scope
<mark>was/were</mark> enabled to use the following weak protocols.
According to National Institute of Standards and Procedures Special
Publication 800-52 Revision 2, these protocols no longer meet the
security needs of entities implementing strong cryptography to protect
data over public or untrusted communication channels. <mark>In addition,
we found that the recommended TLS 1.3 protocol was not
supported.</mark></p>
<p><mark>OR</mark></p>
<p><mark>We identified that the servers in scope did not support TLS
1.3.</mark></p>
<table>
<caption><p>: Identified SSL/TLS protocols</p></caption>
<colgroup>
<col style="width: 12%" />
<col style="width: 12%" />
<col style="width: 12%" />
<col style="width: 12%" />
<col style="width: 12%" />
<col style="width: 12%" />
<col style="width: 12%" />
<col style="width: 12%" />
</colgroup>
<thead>
<tr>
<th>IP Address</th>
<th>TCP Port</th>
<th>SSLv2</th>
<th>SSLv3</th>
<th>TLS 1.0</th>
<th>TLS 1.1</th>
<th>TLS 1.2</th>
<th>TLS 1.3</th>
</tr>
</thead>
<tbody>
<tr>
<td>*******</td>
<td>443,3389</td>
<td>Disabled</td>
<td>Disabled</td>
<td>Disabled</td>
<td>Enabled</td>
<td>Enabled</td>
<td>Not supported</td>
</tr>
</tbody>
</table>
<p>Furthermore, we identified the following insecure SSL/TLS settings on
the server<mark>s</mark> in scope:</p>
<table>
<caption><p>: Identified SSL/TLS cipher suite properties</p></caption>
<colgroup>
<col style="width: 9%" />
<col style="width: 10%" />
<col style="width: 14%" />
<col style="width: 16%" />
<col style="width: 17%" />
<col style="width: 17%" />
<col style="width: 14%" />
</colgroup>
<thead>
<tr>
<th>IP Address</th>
<th style="text-align: center;">TCP Port</th>
<th style="text-align: center;">RC4 block ciphers</th>
<th style="text-align: center;">3DES block ciphers</th>
<th style="text-align: center;">Export cipher suites</th>
<th style="text-align: center;">Obsolete CBC ciphers</th>
<th style="text-align: center;">PFS</th>
</tr>
</thead>
<tbody>
<tr>
<td>*******</td>
<td style="text-align: center;">443</td>
<td style="text-align: center;">Disabled</td>
<td style="text-align: center;">Disabled</td>
<td style="text-align: center;">Enabled</td>
<td style="text-align: center;">Enabled</td>
<td style="text-align: center;">Optional</td>
</tr>
</tbody>
</table>
<p><mark>We also identified that the "**********:443" service was using
a 1024-bit Diffie-Hellman group, which could make the server vulnerable
to Logjam attack.</mark></p>
<table>
<caption><p>: Legend of insecure SSL/TLS protocols and cipher
suites</p></caption>
<colgroup>
<col style="width: 4%" />
<col style="width: 95%" />
</colgroup>
<thead>
<tr>
<th>Mark</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td></td>
<td>No risk identified</td>
</tr>
<tr>
<td></td>
<td>Vulnerable and/or deprecated protocol/cipher suite was allowed</td>
</tr>
<tr>
<td></td>
<td>The recommended protocol was not supported</td>
</tr>
<tr>
<td></td>
<td>The cipher suite was optional to use by the client</td>
</tr>
</tbody>
</table>
<p>The following screenshot shows the potential attacks based on the
results of the "testssl.sh" tool:</p>
<figure>
<img src="media/image1.jpg" style="width:6.21667in;height:4.13167in"
alt="A cat sleeping on the ground Description automatically generated" />
<figcaption><p>: Potential SSL/TLS protocol
vulnerabilities</p></figcaption>
</figure>
<h3 id="impact">Impact</h3>
<p>Successful exploitation could allow an attacker to decrypt and read
or modify intercepted traffic that may contain sensitive information,
such as a user’s login password or other personal information. However,
the vulnerability only affects users for whom the attacker can intercept
traffic. Confidentiality and integrity impacts are limited to the
context of the intercepted user.</p>
<h3 id="likelihood">Likelihood</h3>
<p>The likelihood of exploiting such vulnerabilities depends on several
factors, including the deployment of the systems involved, the security
controls in place and the behavior of the users accessing the
systems.</p>
<p>While the attacker does not need to be authenticated to the service,
they would need to be on designated networks in a Man-in-the-Middle
position to abuse these issues.</p>
<h3 id="recommendation">Recommendation</h3>
<p>Improve the TLS/SSL configuration of the affected systems by
considering the following:</p>
<ul>
<li><p>For SSL/TLS protocols:</p></li>
<li><p>Disable support for the SSL version 2 protocol due to known
weaknesses in protocol design and because it exposes users to the DROWN
attack.</p></li>
<li><p>Investigate whether SSL version 3 is required by application
users. If possible, disable support for the SSL version 3 protocol
because it exposes users to the POODLE attack.</p></li>
<li><p>Investigate whether TLS version 1.0 is required by application
users. If possible, disable support for the TLS version 1.0 protocol
because it exposes users to the BEAST attack.</p></li>
<li><p>Investigate whether TLS version 1.1 is required by application
users. If possible, disable support for the TLS version 1.1 protocol
because it is deprecated and it might expose users to cryptographic
attacks.</p></li>
<li><p>Enable support for the TLS version 1.2 protocol.</p></li>
<li><p>Consider enabling support for the TLS version 1.3
protocol.</p></li>
<li><p>For SSL/TLS cipher suites:</p></li>
<li><p>Enable and prioritize Perfect Forward Secrecy (PFS) ciphers
(those using the DHE/ECDHE algorithm) to support Forward
Secrecy.</p></li>
<li><p>Disable support for weak cipher suites in the service
configuration of the above mentioned services.</p></li>
<li><p>Investigate whether TLS compression is required by application
users. If possible, disable TLS compression because it exposes users to
the CRIME attack.</p></li>
<li><p>Investigate whether HTTP compression is required by application
users. If possible, disable HTTP compression because it exposes users to
the BREACH attack.</p></li>
<li><p>Change the TLS configuration on the IP:PORT service to use a
Diffie-Hellman group that is at least 2048 bit and unique, as the one
currently used exposes the service to the Logjam attack.</p></li>
</ul>
<p><em>Note: Disabling old and insecure SSL versions (such as SSLv3, TLS
1.0 or TLS 1.1) will improve security, however, outdated clients may not
support secure versions such as TLS 1.2 or TLS 1.3. Disabling cipher
suites using CBC might also affect out-of-date clients. This could mean
that clients (human users via web browsers or services using APIs) of
the application will not be able to connect to the application.
Therefore, we recommend researching whether the most secure TLS versions
and cipher suites support all use cases.</em></p>
<h3 id="reference">Reference</h3>
<p>National Institute of Standards and Technology. “Guidelines for the
Selection, Configuration, and Use of Transport Layer Security (TLS)
Implementations”, NIST SP 800-52 Revision 2:
https://csrc.nist.gov/publications/detail/sp/800-52/rev-2/final,
published August 2019.</p>
<p>The following CVE<mark>s</mark> <mark>are</mark> connected to the
finding mentioned above:</p>
<table>
<caption><p>: CVE ID and CVSS score</p></caption>
<colgroup>
<col style="width: 16%" />
<col style="width: 24%" />
<col style="width: 15%" />
<col style="width: 18%" />
<col style="width: 11%" />
<col style="width: 13%" />
</colgroup>
<thead>
<tr>
<th>CVE Identifier</th>
<th>Vulnerability Name</th>
<th>Publish Date</th>
<th>CVSSv2 Score</th>
<th>Access</th>
<th>Complexity</th>
</tr>
</thead>
<tbody>
<tr>
<td>CVE-2016-2183</td>
<td>SWEET32, DES</td>
<td>2016-08-31</td>
<td>5.0</td>
<td>Remote</td>
<td>Low</td>
</tr>
<tr>
<td>CVE-2016-6329</td>
<td>SWEET32, Blowfish</td>
<td>2017-01-31</td>
<td>4.3</td>
<td>Remote</td>
<td>Medium</td>
</tr>
<tr>
<td>CVE-2016-0800</td>
<td>DROWN</td>
<td>2016-03-01</td>
<td>4.3</td>
<td>Remote</td>
<td>Medium</td>
</tr>
<tr>
<td>CVE-2015-4000</td>
<td>LOGJAM</td>
<td>2015-05-20</td>
<td>4.3</td>
<td>Remote</td>
<td>Medium</td>
</tr>
<tr>
<td>CVE-2015-2808</td>
<td>RC4</td>
<td>2015-03-31</td>
<td>4.3</td>
<td>Remote</td>
<td>Medium</td>
</tr>
<tr>
<td>CVE-2015-0204</td>
<td>FREAK</td>
<td>2015-01-08</td>
<td>4.3</td>
<td>Remote</td>
<td>Medium</td>
</tr>
<tr>
<td>CVE-2014-3566</td>
<td>POODLE</td>
<td>2014-10-04</td>
<td>4.3</td>
<td>Remote</td>
<td>Medium</td>
</tr>
<tr>
<td>CVE-2014-2566</td>
<td>RC4</td>
<td>2013-03-15</td>
<td>4.3</td>
<td>Remote</td>
<td>Medium</td>
</tr>
<tr>
<td>CVE-2011-3389</td>
<td>BEAST</td>
<td>2011-09-10</td>
<td>4.3</td>
<td>Remote</td>
<td>Medium</td>
</tr>
<tr>
<td>CVE-2013-3587</td>
<td>BREACH</td>
<td>2012-09-20</td>
<td>4.3</td>
<td>Remote</td>
<td>High</td>
</tr>
<tr>
<td>CVE-2012-4929</td>
<td>CRIME</td>
<td>2012-09-15</td>
<td>2.6</td>
<td>Remote</td>
<td>High</td>
</tr>
<tr>
<td>CVE-2013-0169</td>
<td>LUCKY 13</td>
<td>2013-02-08</td>
<td>2.6</td>
<td>Remote</td>
<td>High</td>
</tr>
</tbody>
</table>
<h3 id="status-and-retest-notes">Status and Retest Notes</h3>
<p>Open</p>
