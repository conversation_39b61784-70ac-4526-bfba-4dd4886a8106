#!/usr/bin/env python3
"""
generate_testssl_report.py

Create a DOCX finding (based on a template) and a Markdown report from a testssl.sh JSON output.

Usage:
  python generate_testssl_report.py \
      --input testssl.json \
      --template EXAMPLE_TLS_ISSUE.docm \
      --out-docx TestSSL-Finding.docx \
      --out-md TestSSL-Finding.md \
      --project "Acme Corp External" \
      --target "app.acme.example:443"
"""

import argparse
import json
import os
import re
from datetime import datetime
from collections import defaultdict
from zipfile import ZipFile

# Optional imports (only needed if a Jinja template is provided)
try:
    from docxtpl import DocxTemplate, InlineImage
    from docx.shared import Mm
    HAS_DOXCTPL = True
except Exception:
    HAS_DOXCTPL = False

try:
    from docx import Document  # fallback DOCX writer / appender
    from docx.shared import Pt
except Exception:
    Document = None


SEVERITY_ORDER = ["CRITIC<PERSON>", "HIGH", "<PERSON>DI<PERSON>", "LOW", "INFO", "OK", "WARN", "WARNING", "ERROR", "FATAL", "UNKNOWN"]
SEVERITY_RANK = {s: i for i, s in enumerate(SEVERITY_ORDER)}


def parse_args():
    p = argparse.ArgumentParser(description="Create DOCX & Markdown report from testssl.sh JSON output.")
    p.add_argument("--input", "-i", required=True, help="Path to testssl.sh JSON output")
    p.add_argument("--template", "-t", default=None, help="Path to DOCX/DOCM template. If it contains docxtpl tokens, they will be rendered; otherwise content will be appended.")
    p.add_argument("--out-docx", "-o", default="TestSSL-Finding.docx", help="Output DOCX path")
    p.add_argument("--out-md", "-m", default="TestSSL-Finding.md", help="Output Markdown path")
    p.add_argument("--project", default="", help="Project / Engagement name")
    p.add_argument("--target", default="", help="Target host:port or URL")
    p.add_argument("--title", default="TLS/SSL Configuration Issues", help="Finding title")
    return p.parse_args()


def load_json(path):
    with open(path, "r", encoding="utf-8") as f:
        return json.load(f)


def has_jinja_tokens(docx_path):
    """Return True if the docx/docm contains {{ }} or {% %} tokens anywhere."""
    try:
        with ZipFile(docx_path, 'r') as z:
            for name in z.namelist():
                if name.startswith("word/") and name.endswith(".xml"):
                    data = z.read(name).decode("utf-8", errors="ignore")
                    data_no_xml = re.sub(r"<[^>]+>", "", data)
                    if re.search(r"(\{\{.*?\}\}|\{\%.*?\%\})", data_no_xml, flags=re.DOTALL):
                        return True
    except Exception:
        return False
    return False


def _coalesce(*vals, default=None):
    for v in vals:
        if v is not None and v != "":
            return v
    return default


def normalize_results(data):
    items = []

    if isinstance(data, dict):
        candidates = []
        for k in ["scanResult", "results", "issues", "data", "findings", "scan", "output"]:
            if k in data and isinstance(data[k], list):
                candidates = data[k]
                break
        if not candidates:
            candidates = [data]
    elif isinstance(data, list):
        candidates = data
    else:
        candidates = []

    for entry in candidates:
        _id = _coalesce(entry.get("id"), entry.get("testid"), entry.get("check"), entry.get("name"), default="")
        severity = _coalesce(entry.get("severity"), entry.get("severity_level"), entry.get("level"), default="INFO")
        severity = str(severity).upper()
        if severity == "WARNING":
            severity = "WARN"
        finding = _coalesce(entry.get("finding"), entry.get("message"), entry.get("desc"), entry.get("result"), default="")
        cve = entry.get("cve") or entry.get("cves") or []
        if isinstance(cve, str):
            cve = [cve] if cve else []
        ip = _coalesce(entry.get("ip"), entry.get("ip_addr"), default="")
        port = _coalesce(entry.get("port"), entry.get("dst_port"), default="")
        host = _coalesce(entry.get("hostname"), entry.get("host"), entry.get("fqdn"), default="")
        timestamp = _coalesce(entry.get("timestamp"), entry.get("scanTime"), entry.get("time"), default=None)
        protocol = _coalesce(entry.get("sslProto"), entry.get("protocol"), default="")
        cipher = _coalesce(entry.get("cipher"), entry.get("cipherSuite"), default="")
        key_exchange = _coalesce(entry.get("keyExchange"), entry.get("kx"), default="")

        items.append({
            "id": _id or "",
            "severity": severity,
            "finding": finding or "",
            "cve": cve,
            "ip": ip,
            "port": port,
            "host": host,
            "timestamp": timestamp,
            "protocol": protocol,
            "cipher": cipher,
            "key_exchange": key_exchange,
        })

    return items


def severity_sort_key(issue):
    return SEVERITY_RANK.get(issue.get("severity", "UNKNOWN"), len(SEVERITY_ORDER))


def classify_issue(issue):
    txt = f"{issue.get('id','')} {issue.get('finding','')}".lower()

    def has(any_of):
        return any(x in txt for x in any_of)

    category = "TLS/SSL Misconfiguration"
    remediation = "Review and update TLS/SSL configuration according to current best practices."

    if has(["heartbleed", "cve-2014-0160"]):
        category = "OpenSSL Heartbleed"
        remediation = "Upgrade OpenSSL to a non-vulnerable version and restart services."
    elif has(["poodle", "ssl3"]):
        category = "POODLE / SSLv3 Supported"
        remediation = "Disable SSLv3 and use TLS 1.2+ only."
    elif has(["tlsv1.0", "tls 1.0", "tls1_0", "tls1.0"]):
        category = "Legacy Protocol Enabled (TLS 1.0)"
        remediation = "Disable TLS 1.0 (and TLS 1.1); enforce TLS 1.2+."
    elif has(["tlsv1.1", "tls 1.1", "tls1_1", "tls1.1"]):
        category = "Legacy Protocol Enabled (TLS 1.1)"
        remediation = "Disable TLS 1.1; enforce TLS 1.2+."
    elif has(["rc4", "3des", "cbc is vulnerable", "weak cipher", "insecure cipher"]):
        category = "Weak/Deprecated Cipher Suites"
        remediation = "Remove weak ciphers (e.g., RC4, 3DES) and prefer modern AEAD ciphers like TLS_AES_* or ECDHE+AES-GCM."
    elif has(["self-signed", "self signed"]):
        category = "Self-Signed Certificate"
        remediation = "Use a certificate issued by a trusted CA."
    elif has(["expired", "not yet valid"]):
        category = "Invalid Certificate (Expired/Not Yet Valid)"
        remediation = "Replace the certificate with a valid one; ensure system time is correct."
    elif has(["sha-1", "sha1"]):
        category = "Certificate Signed with SHA-1"
        remediation = "Reissue the certificate using SHA-256 or stronger."
    elif has(["hsts", "strict-transport-security"]):
        category = "Missing or Weak HSTS"
        remediation = "Enable HSTS with a sufficiently long max-age and includeSubDomains where appropriate."
    elif has(["ocsp", "stapling"]):
        category = "OCSP Stapling Not Enabled"
        remediation = "Enable OCSP stapling to improve revocation checking performance."
    elif has(["renegotiation", "insecure renegotiation"]):
        category = "Insecure Renegotiation"
        remediation = "Disable insecure renegotiation; ensure only secure renegotiation is permitted."

    return category, remediation


def summarize(issues):
    counts = defaultdict(int)
    for i in issues:
        counts[i.get("severity", "UNKNOWN")] += 1

    times = []
    for i in issues:
        ts = i.get("timestamp")
        if ts:
            for fmt in ("%Y-%m-%dT%H:%M:%S", "%Y-%m-%d %H:%M:%S", "%Y-%m-%dT%H:%M:%SZ"):
                try:
                    times.append(datetime.strptime(ts[:19], fmt))
                    break
                except Exception:
                    pass
    scan_date = min(times).strftime("%Y-%m-%d %H:%M:%S") if times else datetime.utcnow().strftime("%Y-%m-%d %H:%M:%S UTC")

    return {
        "by_severity": dict(sorted(counts.items(), key=lambda kv: SEVERITY_RANK.get(kv[0], 999))),
        "scan_date": scan_date,
        "total_issues": len(issues),
    }


def build_context(args, issues):
    enriched = []
    for i in issues:
        category, remediation = classify_issue(i)
        enriched.append({
            **i,
            "category": category,
            "remediation": remediation,
            "cve_str": ", ".join(i.get("cve", []) or []),
            "endpoint": ":".join(x for x in [i.get("host") or i.get("ip") or "", str(i.get("port") or "")] if x),
        })

    enriched.sort(key=severity_sort_key)
    summary = summarize(enriched)

    context = {
        "title": args.title,
        "project": args.project,
        "target": args.target,
        "scan_date": summary["scan_date"],
        "total_issues": summary["total_issues"],
        "counts": [{"severity": k, "count": v} for k, v in summary["by_severity"].items()],
        "issues": enriched,
    }
    return context


def render_docx_with_template(template_path, context, out_path):
    """Render using docxtpl if there are Jinja tokens; otherwise append content to the template."""
    if has_jinja_tokens(template_path):
        if not HAS_DOXCTPL:
            raise SystemExit("Template has Jinja tokens, but 'docxtpl' is not installed. Please run: pip install docxtpl")
        tpl = DocxTemplate(template_path)
        tpl.render(context)
        tpl.save(out_path)
        return

    # No Jinja tokens: append content after the template using python-docx
    if Document is None:
        raise SystemExit("python-docx is required to append to the provided template. Please run: pip install python-docx")
    doc = Document(template_path)

    doc.add_page_break()
    doc.add_heading(context["title"], level=1)
    if context.get("project"):
        p = doc.add_paragraph()
        p.add_run("Project: ").bold = True
        p.add_run(context["project"])
    if context.get("target"):
        p = doc.add_paragraph()
        p.add_run("Target: ").bold = True
        p.add_run(context["target"])
    p = doc.add_paragraph()
    p.add_run("Scan date: ").bold = True
    p.add_run(context["scan_date"])

    # Severity counts
    doc.add_heading("Summary", level=2)
    table = doc.add_table(rows=1, cols=2)
    hdr_cells = table.rows[0].cells
    hdr_cells[0].text = "Severity"
    hdr_cells[1].text = "Count"
    for c in context["counts"]:
        row = table.add_row().cells
        row[0].text = str(c["severity"])
        row[1].text = str(c["count"])

    doc.add_heading("Findings", level=2)
    for idx, i in enumerate(context["issues"], 1):
        doc.add_heading(f"{idx}. {i['category']}", level=3)
        p = doc.add_paragraph()
        p.add_run("Severity: ").bold = True
        p.add_run(i.get("severity", ""))

        if i.get("endpoint"):
            p = doc.add_paragraph()
            p.add_run("Endpoint: ").bold = True
            p.add_run(i["endpoint"])

        if i.get("protocol"):
            p = doc.add_paragraph()
            p.add_run("Protocol: ").bold = True
            p.add_run(i["protocol"])
        if i.get("cipher"):
            p = doc.add_paragraph()
            p.add_run("Cipher: ").bold = True
            p.add_run(i["cipher"])

        p = doc.add_paragraph()
        p.add_run("Detail: ").bold = True
        p.add_run(i.get("finding", ""))

        if i.get("cve_str"):
            p = doc.add_paragraph()
            p.add_run("CVE: ").bold = True
            p.add_run(i["cve_str"])

        doc.add_paragraph()  # spacer
        doc.add_paragraph("Remediation:", style="List Bullet")
        doc.add_paragraph(i["remediation"])

    doc.save(out_path)


def write_markdown(context, out_path):
    lines = []
    lines.append(f"# {context['title']}")
    if context.get("project"):
        lines.append(f"**Project:** {context['project']}")
    if context.get("target"):
        lines.append(f"**Target:** {context['target']}")
    lines.append(f"**Scan date:** {context['scan_date']}")
    lines.append("")
    lines.append("## Summary")
    lines.append("| Severity | Count |")
    lines.append("|---|---:|")
    for c in context["counts"]:
        lines.append(f"| {c['severity']} | {c['count']} |")
    lines.append("")
    lines.append("## Findings")
    for idx, i in enumerate(context["issues"], 1):
        lines.append(f"### {idx}. {i['category']}")
        lines.append(f"- **Severity:** {i.get('severity','')}")
        if i.get("endpoint"):
            lines.append(f"- **Endpoint:** {i['endpoint']}")
        if i.get("protocol"):
            lines.append(f"- **Protocol:** {i['protocol']}")
        if i.get("cipher"):
            lines.append(f"- **Cipher:** {i['cipher']}")
        if i.get("cve_str"):
            lines.append(f"- **CVE:** {i['cve_str']}")
        lines.append("")
        detail = i.get("finding", "").strip()
        if detail:
            lines.append("**Detail**")
            lines.append("")
            lines.append(detail)
            lines.append("")
        lines.append("**Remediation**")
        lines.append("")
        lines.append(i["remediation"]) 
        lines.append("")
        lines.append("---")
        lines.append("")

    with open(out_path, "w", encoding="utf-8") as f:
        f.write("\n".join(lines))


def main():
    args = parse_args()

    data = load_json(args.input)
    issues = normalize_results(data)
    ctx = build_context(args, issues)

    if args.template:
        render_docx_with_template(args.template, ctx, args.out_docx)
    else:
        # No template provided: write a simple standalone DOCX
        if Document is None:
            raise SystemExit("python-docx is required. Please run: pip install python-docx")
        doc = Document()
        doc.add_heading(ctx["title"], level=1)
        if ctx.get("project"):
            p = doc.add_paragraph()
            p.add_run("Project: ").bold = True
            p.add_run(ctx["project"])
        if ctx.get("target"):
            p = doc.add_paragraph()
            p.add_run("Target: ").bold = True
            p.add_run(ctx["target"])
        p = doc.add_paragraph()
        p.add_run("Scan date: ").bold = True
        p.add_run(ctx["scan_date"])

        doc.add_heading("Summary", level=2)
        table = doc.add_table(rows=1, cols=2)
        hdr_cells = table.rows[0].cells
        hdr_cells[0].text = "Severity"
        hdr_cells[1].text = "Count"
        for c in ctx["counts"]:
            row = table.add_row().cells
            row[0].text = str(c["severity"])
            row[1].text = str(c["count"])

        doc.add_heading("Findings", level=2)
        for idx, i in enumerate(ctx["issues"], 1):
            doc.add_heading(f"{idx}. {i['category']}", level=3)
            p = doc.add_paragraph()
            p.add_run("Severity: ").bold = True
            p.add_run(i.get("severity", ""))

            if i.get("endpoint"):
                p = doc.add_paragraph()
                p.add_run("Endpoint: ").bold = True
                p.add_run(i["endpoint"])

            if i.get("protocol"):
                p = doc.add_paragraph()
                p.add_run("Protocol: ").bold = True
                p.add_run(i["protocol"])
            if i.get("cipher"):
                p = doc.add_paragraph()
                p.add_run("Cipher: ").bold = True
                p.add_run(i["cipher"])

            p = doc.add_paragraph()
            p.add_run("Detail: ").bold = True
            p.add_run(i.get("finding", ""))

            if i.get("cve_str"):
                p = doc.add_paragraph()
                p.add_run("CVE: ").bold = True
                p.add_run(i["cve_str"])

            doc.add_paragraph()  # spacer
            doc.add_paragraph("Remediation:", style="List Bullet")
            doc.add_paragraph(i["remediation"])

        doc.save(args.out_docx)

    write_markdown(ctx, args.out_md)
    print(f"Done. DOCX: {args.out_docx}  |  Markdown: {args.out_md}")


if __name__ == "__main__":
    main()
